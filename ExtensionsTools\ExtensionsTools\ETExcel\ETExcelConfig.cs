﻿using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;

namespace ET
{
    /// <summary>
    /// Excel配置管理类，提供模板工作簿操作和数据加载功能
    /// </summary>
    /// <remarks>
    /// 此类主要负责：
    /// 1. 管理Excel应用程序实例和模板工作簿
    /// 2. 提供模板工作表的复制和操作功能
    /// 3. 从模板工作簿加载各种配置数据
    /// 4. 管理模板工作簿的打开和关闭 /// 主要功能模块：
    /// - 模板工作簿管理（打开/关闭/复制）
    /// - 数据加载（前缀后缀替换、行政区域、邮件联系人等）
    /// - 配置数据结构定义 /// 使用场景：
    /// - 需要从模板文件加载配置数据时
    /// - 需要复制模板工作表到目标工作簿时
    /// - 需要管理Excel应用程序状态时
    /// </remarks>
    public class ETExcelConfig
    {
        #region 变量

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public static Application XlApp;

        /// <summary>
        /// 模板Excel文件的路径
        /// </summary>
        public static string TemplateExcelPath = ETConfig.GetConfigDirectory("hyExcelTemplate.xlsx", ".template");

        /// <summary>
        /// 模板工作簿实例，用于后台操作
        /// </summary>
        private static Workbook _templateWorkbook;

        #endregion 变量

        #region 打开关闭模板表

        /// <summary>
        /// 从模板工作簿复制指定工作表到目标工作簿
        /// </summary>
        /// <param name="sheetName">要复制的工作表名称</param>
        /// <param name="destinationWorkbook">目标工作簿</param>
        /// <returns>复制后的新工作表，如果失败则返回null</returns>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 在后台打开模板工作簿
        /// 2. 查找指定名称的工作表
        /// 3. 将工作表复制到目标工作簿
        /// 4. 关闭模板工作簿 /// 注意：此方法会自动管理模板工作簿的生命周期， 调用者无需手动打开或关闭模板工作簿
        /// </remarks>
        public static Worksheet CopyTemplateSheetFromExcelDnaData(string sheetName, Workbook destinationWorkbook)
        {
            // 尝试在后台打开模板工作簿
            if (!OpenTemplateWorkbookInBackground()) return null;

            // 查找指定名称的工作表
            Worksheet templateSheet = _templateWorkbook.GetWorksheetByName(sheetName);
            if (templateSheet == null) return null;

            // 复制工作表到目标工作簿
            Worksheet newSheet = templateSheet.CopyWorksheetTo(destinationWorkbook);
            // 关闭模板工作簿
            CloseTemplateWorkbook();
            return newSheet;
        }

        /// <summary>
        /// 在后台打开模板工作簿
        /// </summary>
        /// <param name="showWindow">是否显示模板工作簿窗口，默认为false（后台打开）</param>
        /// <returns>成功打开模板工作簿则返回true，否则返回false</returns>
        /// <remarks>此方法会在后台打开模板工作簿，不影响当前活动的工作簿</remarks>
        public static bool OpenTemplateWorkbookInBackground(bool showWindow = false)
        {
            if (_templateWorkbook == null)
            {
                _templateWorkbook = ETExcelExtensions.OpenWorkbook(TemplateExcelPath);
                Workbook currentWorkbook = XlApp.ActiveWorkbook; // 记录当前工作簿
                _templateWorkbook.Activate();
                XlApp.ActiveWindow.Visible = showWindow;
                currentWorkbook.Activate(); // 重新激活原工作簿
            }

            return _templateWorkbook != null;
        }

        /// <summary>
        /// 关闭模板工作簿
        /// </summary>
        /// <returns>成功关闭模板工作簿则返回true，否则返回false</returns>
        /// <remarks>此方法会关闭模板工作簿，不保存任何更改</remarks>
        public static bool CloseTemplateWorkbook()
        {
            try
            {
                if (_templateWorkbook == null) return false;
                _templateWorkbook.Close(false);
                _templateWorkbook = null; // 清空引用
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                return false;
            }
        }

        #endregion 打开关闭模板表

        #region 加载数据

        /// <summary>
        /// 从模板工作簿获取指定工作表的数据
        /// </summary>
        /// <param name="worksheetName">工作表名称</param>
        /// <returns>二维对象数组，包含工作表所有单元格内容；若找不到模板工作表，则返回null</returns>
        /// <remarks>此方法会在后台打开模板工作簿，读取数据后自动关闭</remarks>
        public static object[,] LoadDataFromTemplateWorkbook(string worksheetName)
        {
            if (!OpenTemplateWorkbookInBackground()) return null;

            Worksheet templateSheet = _templateWorkbook.GetWorksheetByName(worksheetName);
            object[,] data = templateSheet?.Cells.OptimizeRangeSize().Value;

            CloseTemplateWorkbook();
            return data;
        }

        /// <summary>
        /// 加载前缀后缀替换规则
        /// </summary>
        /// <param name="worksheetName">包含替换规则的工作表名称</param>
        /// <returns>包含替换规则的字典，如果加载失败则返回null</returns>
        /// <remarks>此方法从指定工作表加载前缀后缀替换规则，并将其存储在字典中</remarks>
        private static Dictionary<string, PrefixSuffixReplace> LoadPrefixSuffixReplacements(string worksheetName)
        {
            object[,] dataArray = LoadDataFromTemplateWorkbook(worksheetName);
            if (dataArray == null) return null;

            Dictionary<string, PrefixSuffixReplace> replacements = [];
            for (int i = 1; i < dataArray.GetLength(0); i++) // 从第二行开始，跳过表头
            {
                if (dataArray[i, 1] == null || string.IsNullOrEmpty(dataArray[i, 1].ToString())) continue;

                PrefixSuffixReplace replacement = new()
                {
                    Character = dataArray[i, 1].ToString(),
                    ReplacementCharacter = dataArray[i, 2]?.ToString() ?? string.Empty,
                    Category = dataArray[i, 3]?.ToString() ?? string.Empty
                };
                replacements[replacement.Character] = replacement;
            }

            return replacements;
        }

        /// <summary>
        /// 加载行政区域信息
        /// </summary>
        /// <param name="worksheetName">包含行政区域信息的工作表名称</param>
        /// <returns>包含行政区域信息的字典，如果加载失败则返回null</returns>
        /// <remarks>此方法从指定工作表加载行政区域信息，并将其存储在字典中</remarks>
        private static Dictionary<string, AdministrativeRegion> LoadAdministrativeRegions(string worksheetName)
        {
            object[,] dataArray = LoadDataFromTemplateWorkbook(worksheetName);
            if (dataArray == null) return null;

            Dictionary<string, AdministrativeRegion> regions = [];
            for (int i = 1; i < dataArray.GetLength(0); i++) // 从第二行开始，跳过表头
            {
                if (dataArray[i, 1] == null || string.IsNullOrEmpty(dataArray[i, 1].ToString())) continue;

                AdministrativeRegion region = new()
                {
                    UpperLevel = dataArray[i, 1].ToString(),
                    LowerLevel = dataArray[i, 2]?.ToString() ?? string.Empty,
                    Category = dataArray[i, 3]?.ToString() ?? string.Empty
                };
                regions[region.UpperLevel] = region;
            }

            return regions;
        }

        /// <summary>
        /// 加载邮件联系人信息
        /// </summary>
        /// <param name="worksheetName">包含邮件联系人信息的工作表名称</param>
        /// <returns>包含邮件联系人信息的字典，如果加载失败则返回null</returns>
        /// <remarks>此方法从指定工作表加载邮件联系人信息，并将其存储在字典中</remarks>
        private static Dictionary<string, EmailContact> LoadEmailContacts(string worksheetName)
        {
            object[,] dataArray = LoadDataFromTemplateWorkbook(worksheetName);
            if (dataArray == null) return null;

            Dictionary<string, EmailContact> contacts = [];
            for (int i = 1; i < dataArray.GetLength(0); i++) // 从第二行开始，跳过表头
            {
                if (dataArray[i, 1] == null || string.IsNullOrEmpty(dataArray[i, 1].ToString())) continue;

                EmailContact contact = new()
                {
                    Name = dataArray[i, 1].ToString(),
                    EmailAddress = dataArray[i, 2]?.ToString() ?? string.Empty,
                    Note = dataArray[i, 3]?.ToString() ?? string.Empty
                };
                contacts[contact.Name] = contact;
            }

            return contacts;
        }

        /// <summary>
        /// 加载勘误信息
        /// </summary>
        /// <param name="worksheetName">包含勘误信息的工作表名称</param>
        /// <returns>包含勘误信息的字典，如果加载失败则返回null</returns>
        /// <remarks>此方法从指定工作表加载勘误信息，并将其存储在字典中</remarks>
        private static Dictionary<string, Correction> LoadCorrections(string worksheetName)
        {
            object[,] dataArray = LoadDataFromTemplateWorkbook(worksheetName);
            if (dataArray == null) return null;

            Dictionary<string, Correction> corrections = [];
            for (int i = 1; i < dataArray.GetLength(0); i++) // 从第二行开始，跳过表头
            {
                if (dataArray[i, 1] == null || string.IsNullOrEmpty(dataArray[i, 1].ToString())) continue;

                Correction correction = new()
                {
                    IncorrectWord = dataArray[i, 1].ToString(),
                    CorrectWord = dataArray[i, 2]?.ToString() ?? string.Empty
                };
                corrections[correction.IncorrectWord] = correction;
            }

            return corrections;
        }

        /// <summary>
        /// 加载分隔符信息
        /// </summary>
        /// <param name="worksheetName">包含分隔符信息的工作表名称</param>
        /// <returns>包含分隔符信息的字典，如果加载失败则返回null</returns>
        /// <remarks>此方法从指定工作表加载分隔符信息，并将其存储在字典中</remarks>
        private static Dictionary<string, Separator> LoadSeparators(string worksheetName)
        {
            object[,] dataArray = LoadDataFromTemplateWorkbook(worksheetName);
            if (dataArray == null) return null;

            Dictionary<string, Separator> separators = [];
            for (int i = 1; i < dataArray.GetLength(0); i++) // 从第二行开始，跳过表头
            {
                if (dataArray[i, 1] == null || string.IsNullOrEmpty(dataArray[i, 1].ToString())) continue;

                Separator separator = new()
                {
                    Delimiter = dataArray[i, 1].ToString(),
                    Category = dataArray[i, 2]?.ToString() ?? string.Empty
                };
                separators[separator.Delimiter] = separator;
            }

            return separators;
        }

        #endregion 加载数据
    }

    #region 结构体定义

    /// <summary>
    /// 表示一个前缀/后缀替换规则
    /// </summary>
    public struct PrefixSuffixReplace
    {
        /// <summary>
        /// 需要被替换的字符
        /// </summary>
        public string Character;

        /// <summary>
        /// 替换后的字符
        /// </summary>
        public string ReplacementCharacter;

        /// <summary>
        /// 替换规则的类别
        /// </summary>
        public string Category;
    }

    /// <summary>
    /// 表示一个行政区域
    /// </summary>
    public struct AdministrativeRegion
    {
        /// <summary>
        /// 上级行政区域名称
        /// </summary>
        public string UpperLevel;

        /// <summary>
        /// 下级行政区域名称
        /// </summary>
        public string LowerLevel;

        /// <summary>
        /// 行政区域的类别
        /// </summary>
        public string Category;
    }

    /// <summary>
    /// 表示一个邮件联系人信息
    /// </summary>
    public struct EmailContact
    {
        /// <summary>
        /// 联系人的姓名
        /// </summary>
        public string Name;

        /// <summary>
        /// 联系人的电子邮件地址
        /// </summary>
        public string EmailAddress;

        /// <summary>
        /// 与该联系人相关的备注信息
        /// </summary>
        public string Note;
    }

    /// <summary>
    /// 表示一个纠错规则
    /// </summary>
    public struct Correction
    {
        /// <summary>
        /// 错误的单词
        /// </summary>
        public string IncorrectWord;

        /// <summary>
        /// 正确的单词
        /// </summary>
        public string CorrectWord;
    }

    /// <summary>
    /// 表示一个分隔符
    /// </summary>
    public struct Separator
    {
        /// <summary>
        /// 分隔符字符
        /// </summary>
        public string Delimiter;

        /// <summary>
        /// 分隔符的类别
        /// </summary>
        public string Category;
    }

    #endregion 结构体定义
}