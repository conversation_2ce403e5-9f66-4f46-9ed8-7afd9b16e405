using SharpCompress.Archives;
using SharpCompress.Archives.Zip;
using SharpCompress.Common;
using SharpCompress.Writers;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace ET
{
    /// <summary>
    /// 应用程序路径类型枚举
    /// </summary>
    public enum PathType
    {
        /// <summary>
        /// 应用程序目录
        /// </summary>
        Base,

        /// <summary>
        /// 配置目录
        /// </summary>
        Config,

        /// <summary>
        /// XLL插件目录
        /// </summary>
        Xll
    }

    /// <summary>
    /// 文件操作扩展工具类，提供文件路径处理、压缩、长路径支持等功能
    /// </summary>
    /// <remarks>
    /// 主要功能包括：
    /// 1. 文件名非法字符过滤
    /// 2. 路径操作（相对路径、应用程序路径等）
    /// 3. 文件压缩功能
    /// 4. 文件基本操作（复制、删除、读写等）
    /// 5. 长路径支持（超过260字符限制）
    /// </remarks>
    public static class ETFile
    {
        #region 字符处理

        /// <summary>
        /// 生成过滤非法字符后的文件名
        /// </summary>
        /// <param name="fileName">原始文件名</param>
        /// <param name="replacement">用于替换非法字符的字符串，默认为空（使用下划线替换）</param>
        /// <returns>过滤后的文件名，如果输入为空则返回null</returns>
        /// <remarks>此方法会将文件名中的非法字符替换为指定字符或下划线。 非法字符包括文件名和路径中不允许的字符，如 \ / : * ? " &lt; &gt; |</remarks>
        public static string PathRemoveInvalidChars(this string fileName, string replacement = "")
        {
            if (string.IsNullOrEmpty(fileName))
                return null;

            // 获取所有非法字符（文件名和路径的非法字符合并）
            char[] invalidChars = Path.GetInvalidFileNameChars().Concat(Path.GetInvalidPathChars()).ToArray();

            // 使用LINQ替换非法字符
            return new string(
                fileName
                    .Select(c => invalidChars.Contains(c) ?
                        (string.IsNullOrEmpty(replacement) ? '_' : replacement.FirstOrDefault()) : c)
                    .ToArray());
        }

        #endregion 字符处理

        #region 路径操作

        /// <summary>
        /// 更改文件扩展名
        /// </summary>
        /// <param name="fileName">原始文件名（包含完整路径）</param>
        /// <param name="newExtension">新的扩展名（如".txt"）</param>
        /// <returns>更改扩展名后的完整文件路径</returns>
        /// <remarks>此方法会保留原文件的目录路径和文件名，仅更改扩展名部分</remarks>
        public static string ChangeExtension(string fileName, string newExtension)
        {
            string directory = Path.GetDirectoryName(fileName);
            string name = Path.GetFileNameWithoutExtension(fileName);
            return Path.Combine(directory, $"{name}{newExtension}");
        }

        /// <summary>
        /// 获取相对路径
        /// </summary>
        /// <param name="filespec">文件完整路径</param>
        /// <param name="folder">相对路径的基准文件夹</param>
        /// <returns>从基准文件夹到目标文件的相对路径</returns>
        /// <remarks>此方法使用URI类来计算相对路径，能够正确处理不同层级的目录结构</remarks>
        public static string RelativePath(string filespec, string folder)
        {
            Uri pathUri = new(filespec);

            // 确保文件夹路径以目录分隔符结尾
            if (!folder.EndsWith(Path.DirectorySeparatorChar.ToString(), StringComparison.Ordinal))
                folder += Path.DirectorySeparatorChar;

            Uri folderUri = new(folder);

            // 计算相对路径并转换为本地路径格式
            return Uri.UnescapeDataString(
                folderUri
                    .MakeRelativeUri(pathUri)
                    .ToString()
                    .Replace('/', Path.DirectorySeparatorChar));
        }

        /// <summary>
        /// 获取应用程序相关路径
        /// </summary>
        /// <param name="fileName">文件名（会自动过滤非法字符）</param>
        /// <param name="pathType">路径类型：Base=应用程序目录，Config=配置目录，Xll=XLL插件目录</param>
        /// <param name="subDirectory">子目录名称（仅当pathType为Config时有效）</param>
        /// <returns>完整文件路径</returns>
        /// <remarks>
        /// 此方法根据不同的路径类型返回相应的完整文件路径：
        /// - Base: 应用程序基础目录
        /// - Config: 配置文件目录（会自动创建目录）
        /// - Xll: XLL插件所在目录
        /// </remarks>
        public static string ApplicationPath(string fileName, PathType pathType = PathType.Base, string subDirectory = null)
        {
            // 获取应用程序基础目录
            string basePath = AppDomain.CurrentDomain.BaseDirectory;
            string path;

            switch (pathType)
            {
                case PathType.Config:
                    // 构建配置目录路径
                    path = Path.Combine(basePath, ETConfig.CONFIG_FOLDER_NAME);
                    if (!string.IsNullOrEmpty(subDirectory))
                    {
                        path = Path.Combine(path, subDirectory);
                    }
                    // 确保配置目录存在，不存在则创建
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                    break;

                case PathType.Xll:
                    // 获取当前程序集所在目录
                    string assemblyLocation = Assembly.GetExecutingAssembly().Location;
                    path = Directory.GetParent(assemblyLocation)?.FullName ?? basePath;
                    break;

                default:
                    // 默认使用应用程序基础目录
                    path = basePath;
                    break;
            }

            // 如果过滤后的文件名为空（全部都是非法字符），使用默认文件名
            if (string.IsNullOrEmpty(fileName))
            {
                return path;
            }
            else
            {
                return Path.Combine(path, fileName);
            }
        }

        #endregion 路径操作

        #region 压缩功能

        /// <summary>
        /// 压缩指定目录到ZIP文件
        /// </summary>
        /// <param name="sourceDirectory">源目录路径</param>
        /// <param name="destZipFile">目标压缩文件路径</param>
        /// <remarks>使用UTF-8编码确保中文文件名正确显示，压缩包内保持原目录结构</remarks>
        public static void ZipDirectory(string sourceDirectory, string destZipFile)
        {
            using (var fileStream = File.Create(destZipFile))
            using (var writer = WriterFactory.Open(fileStream, ArchiveType.Zip, CompressionType.Deflate))
            {
                // 递归添加目录中的所有文件
                AddDirectoryToArchive(writer, sourceDirectory, Path.GetFileName(sourceDirectory));
            }
        }

        /// <summary>
        /// 递归添加目录中的所有文件到压缩包
        /// </summary>
        /// <param name="writer">压缩包写入器</param>
        /// <param name="sourceDirectory">源目录路径</param>
        /// <param name="entryPrefix">压缩包内的目录前缀</param>
        private static void AddDirectoryToArchive(IWriter writer, string sourceDirectory, string entryPrefix)
        {
            // 添加所有文件
            foreach (string filePath in Directory.GetFiles(sourceDirectory))
            {
                string fileName = Path.GetFileName(filePath);
                string entryName = string.IsNullOrEmpty(entryPrefix) ? fileName : $"{entryPrefix}/{fileName}";

                using (var fileStream = File.OpenRead(filePath))
                {
                    writer.Write(entryName, fileStream, File.GetLastWriteTime(filePath));
                }
            }

            // 递归处理子目录
            foreach (string subDirectory in Directory.GetDirectories(sourceDirectory))
            {
                string dirName = Path.GetFileName(subDirectory);
                string newPrefix = string.IsNullOrEmpty(entryPrefix) ? dirName : $"{entryPrefix}/{dirName}";
                AddDirectoryToArchive(writer, subDirectory, newPrefix);
            }
        }

        /// <summary>
        /// 压缩指定目录（自动生成目标压缩文件路径）
        /// </summary>
        /// <param name="sourceDirectory">源目录路径</param>
        /// <remarks>压缩文件将保存在源目录的同级目录下，文件名为"目录名.zip"</remarks>
        public static void ZipDirectory(string sourceDirectory)
        {
            // 自动生成压缩文件路径：在源目录同级创建同名zip文件
            string destinationZipFilePath =
                $"{Path.Combine(Path.GetDirectoryName(sourceDirectory), Path.GetFileName(sourceDirectory))}.zip";
            ZipDirectory(sourceDirectory, destinationZipFilePath);
        }

        /// <summary>
        /// 压缩指定目录下的所有子目录
        /// </summary>
        /// <param name="rootDirectory">根目录路径</param>
        /// <remarks>为根目录下的每个子目录分别创建对应的ZIP压缩文件</remarks>
        public static void ZipSubDirectories(string rootDirectory)
        {
            // 获取所有子目录
            string[] subDirectories = Directory.GetDirectories(rootDirectory);

            // 为每个子目录创建压缩文件
            foreach (string subDirectory in subDirectories)
            {
                string destZipFile =
                    $"{Path.Combine(Path.GetDirectoryName(subDirectory), Path.GetFileName(subDirectory))}.zip";
                ZipDirectory(subDirectory, destZipFile);
            }
        }

        /// <summary>
        /// 解压缩文件到指定目录（支持ZIP、RAR、7ZIP等多种格式）
        /// </summary>
        /// <param name="archiveFilePath">压缩文件路径</param>
        /// <param name="extractToDirectory">解压目标目录</param>
        /// <param name="overwrite">是否覆盖已存在的文件，默认为true</param>
        /// <remarks>支持的格式：ZIP, RAR, 7ZIP, TAR, GZIP, BZIP2, LZIP, XZ 使用SharpCompress库自动检测压缩格式</remarks>
        public static void ExtractArchive(string archiveFilePath, string extractToDirectory, bool overwrite = true)
        {
            if (!File.Exists(archiveFilePath))
                throw new FileNotFoundException($"压缩文件不存在: {archiveFilePath}");

            // 确保目标目录存在
            if (!Directory.Exists(extractToDirectory))
                Directory.CreateDirectory(extractToDirectory);

            using (var archive = ArchiveFactory.Open(archiveFilePath))
            {
                foreach (var entry in archive.Entries)
                {
                    if (!entry.IsDirectory)
                    {
                        entry.WriteToDirectory(extractToDirectory, new ExtractionOptions()
                        {
                            ExtractFullPath = true,
                            Overwrite = overwrite
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检测压缩文件格式
        /// </summary>
        /// <param name="archiveFilePath">压缩文件路径</param>
        /// <returns>压缩文件格式类型</returns>
        public static ArchiveType? DetectArchiveType(string archiveFilePath)
        {
            if (!File.Exists(archiveFilePath))
                return null;

            try
            {
                using (var archive = ArchiveFactory.Open(archiveFilePath))
                {
                    return archive.Type;
                }
            }
            catch
            {
                return null;
            }
        }

        #endregion 压缩功能

        #region 文件操作

        /// <summary>
        /// 执行文件操作的通用方法
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <param name="operation">要执行的操作</param>
        /// <remarks>此方法在执行操作前会检查路径是否有效和文件是否存在， 只有在文件存在时才会执行指定的操作</remarks>
        private static void ExecuteFileOperation(string path, Action<string> operation)
        {
            // 检查路径有效性和文件存在性
            if (string.IsNullOrWhiteSpace(path) || !File.Exists(path))
                return;

            // 执行指定的文件操作
            operation(path);
        }

        /// <summary>
        /// 获取指定目录下所有文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="extension">文件扩展名过滤器（默认为"*"，表示所有文件）</param>
        /// <returns>文件路径列表，如果目录不存在或发生错误则返回空列表</returns>
        /// <remarks>
        /// 此方法会递归搜索所有子目录。扩展名参数支持以下格式：
        /// - "*": 所有文件
        /// - "txt": 所有.txt文件
        /// - ".txt": 所有.txt文件
        /// </remarks>
        public static List<string> DirectoryAllFiles(string directoryPath, string extension = "*")
        {
            // 检查目录是否存在
            if (!Directory.Exists(directoryPath))
            {
                Debug.WriteLine($"指定的路径不存在：{directoryPath}");
                return [];
            }

            // 标准化扩展名格式
            extension =
                extension == "*" ? "*.*" : (extension.StartsWith(".") ? $"*{extension}" : $"*.{extension}");

            try
            {
                // 递归获取所有匹配的文件
                return Directory
                    .EnumerateFiles(directoryPath, extension, SearchOption.AllDirectories)
                    .ToList();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取文件列表时发生错误：{ex.Message}");
                return [];
            }
        }

        /// <summary>
        /// 检查Visio文件是否比PDF文件新
        /// </summary>
        public static bool FileIsNewer(string path1, string path2)
        { return ETFile.FileLastWriteTime(path1) > ETFile.FileLastWriteTime(path2); }

        /// <summary>
        /// 获取文件的最后写入时间，支持超长文件名
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件的最后写入时间</returns>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题</remarks>
        public static DateTime FileLastWriteTime(this string path)
        {
            string longPath = PathAddLongPathPrefix(path);
            return File.GetLastWriteTime(longPath);
        }

        /// <summary>
        /// 设置文件的最后写入时间，支持超长文件名
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <param name="lastWriteTime">要设置的最后写入时间</param>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题</remarks>
        public static void FileLastWriteTime(this string path, DateTime lastWriteTime)
        {
            string longPath = PathAddLongPathPrefix(path);
            File.SetLastWriteTime(longPath, lastWriteTime);
        }

        /// <summary>
        /// 将文件路径复制到剪贴板
        /// </summary>
        /// <param name="path">文件路径</param>
        public static void FileCopyToClipboard(string path)
        {
            ExecuteFileOperation(path, p =>
            {
                StringCollection stringCollection = [p];
                Clipboard.SetFileDropList(stringCollection);
            });
        }

        // 保持向后兼容性
        public static void FileCopyToClipboard(this FileInfo file)
        {
            if (file == null)
                return;
            FileCopyToClipboard(file.FullName);
        }

        /// <summary>
        /// 设置文件为只读
        /// </summary>
        /// <param name="path">文件路径</param>
        public static void FileSetReadOnly(string path)
        {
            ExecuteFileOperation(path, p => File.SetAttributes(p, File.GetAttributes(p) | FileAttributes.ReadOnly));
        }

        // 保持向后兼容性
        public static void FileSetReadOnly(this FileInfo file)
        {
            if (file == null)
                throw new ArgumentNullException(nameof(file));
            if (!file.Exists)
                throw new ArgumentException($"File does not exist: {file.FullName}");

            FileSetReadOnly(file.FullName);
        }

        #endregion 文件操作

        #region LongPath

        /// <summary>
        /// 为路径添加长路径前缀
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>添加了长路径前缀的路径</returns>
        public static string PathAddLongPathPrefix(string path)
        { return path.StartsWith(@"\\?\") ? path : $@"\\?\{path}"; }

        /// <summary>
        /// 检查并调整文件名长度，确保文件名和路径长度符合系统限制
        /// </summary>
        /// <param name="filePath">原始文件路径</param>
        /// <returns>调整后的文件路径</returns>
        /// <remarks>当文件名或路径超过系统限制时,会自动截断并添加序号</remarks>
        public static string PathAdjustNameLength(string filePath)
        {
            const int MaxFileNameLength = 255;
            const int MaxPathLength = 260;

            string directory = Path.GetDirectoryName(filePath);
            string fileName = Path.GetFileNameWithoutExtension(filePath);
            string extension = Path.GetExtension(filePath);

            if (filePath.Length <= MaxPathLength && fileName.Length <= MaxFileNameLength)
                return filePath;

            // 安全计算最大文件名长度，确保不为负数
            int maxFileNameLength = Math.Min(MaxFileNameLength,
                Math.Max(10, MaxPathLength - directory.Length - extension.Length - 1));
            if (maxFileNameLength <= 10)
                return Path.Combine(directory, $"file{extension}");

            string newFileName =
                $"{fileName.Substring(0, Math.Min(fileName.Length, maxFileNameLength - 3))}~1";
            string newFilePath = Path.Combine(directory, $"{newFileName}{extension}");

            int counter = 1;
            while (File.Exists(newFilePath))
            {
                counter++;
                newFileName =
                    $"{fileName.Substring(0, Math.Min(fileName.Length, maxFileNameLength - 3 - counter.ToString().Length))}~{counter}";
                newFilePath = Path.Combine(directory, $"{newFileName}{extension}");
            }

            return newFilePath;
        }

        /// <summary>
        /// 扩展的文件复制方法，支持超长路径、覆盖选项以及日志功能。
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="destPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖目标文件，默认为false</param>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题，并在复制前后进行必要的检查和日志记录。</remarks>
        public static void FileCopy(this string sourcePath, string destPath, bool overwrite = false)
        {
            // 添加长路径前缀，处理超长路径问题
            string longSourcePath = PathAddLongPathPrefix(sourcePath);
            string longDestPath = PathAddLongPathPrefix(destPath);

            // 检查源文件是否存在
            if (!FileExists(longSourcePath))
            {
                throw new FileNotFoundException("源文件不存在", sourcePath);
            }

            // 如果目标文件存在且不允许覆盖，抛出异常
            if (FileExists(longDestPath) && !overwrite)
            {
                throw new IOException("目标文件已存在，且不允许覆盖");
            }

            try
            {
                // 执行文件复制
                File.Copy(longSourcePath, longDestPath, overwrite);
                Console.WriteLine($"文件复制成功: {sourcePath} -> {destPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"复制文件时发生错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 扩展的文件删除方法，支持超长路径
        /// </summary>
        /// <param name="path">要删除的文件路径</param>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题，并在删除前后进行必要的检查和日志记录。</remarks>
        public static void FileDelete(this string path)
        {
            // 添加长路径前缀，处理超长路径问题
            string longPath = PathAddLongPathPrefix(path);

            // 检查文件是否存在
            if (!FileExists(longPath))
            {
                Console.WriteLine($"要删除的文件不存在: {path}");
                return;
            }

            try
            {
                // 执行文件删除
                File.Delete(longPath);
                Console.WriteLine($"文件删除成功: {path}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除文件时发生错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 扩展的文件或目录存在检查方法，支持超长路径
        /// </summary>
        /// <param name="path">文路径</param>
        /// <returns>文件是否存在</returns>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题，并根据路径类型判断文件或目录是否存在</remarks>
        public static bool FileExists(this string path)
        {
            string longPath = PathAddLongPathPrefix(path);
            return File.Exists(longPath);
        }

        /// <summary>
        /// 扩展的目录存在检查方法，支持超长路径
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>目录是否存在</returns>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题，专门用于检查目录是否存在</remarks>
        public static bool DirectoryExists(this string path)
        {
            string longPath = PathAddLongPathPrefix(path);
            return Directory.Exists(longPath);
        }

        /// <summary>
        /// 读取文件的所有行，支持超长文件名
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件的所有行</returns>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题</remarks>
        public static string[] ReadFileToArray(this string path)
        {
            string longPath = PathAddLongPathPrefix(path);
            return File.ReadAllLines(longPath);
        }

        /// <summary>
        /// 智能读取文本文件的所有行，自动检测编码格式，支持超长文件名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件的所有行</returns>
        /// <remarks>
        /// 此方法会自动尝试多种编码格式读取文件，包括： UTF-8、GBK、GB2312、系统默认编码、ASCII、UTF-16等 通过乱码检测算法自动选择最合适的编码格式 同时支持超长路径处理
        /// </remarks>
        public static string[] ReadAllLinesWithEncoding(string filePath)
        {
            try
            {
                // 尝试多种编码格式读取文件
                Encoding[] encodingsToTry = {
                    Encoding.UTF8,           // UTF-8
                    Encoding.GetEncoding("GBK"),  // GBK (中文)
                    Encoding.GetEncoding("GB2312"), // GB2312 (简体中文)
                    Encoding.Default,        // 系统默认编码
                    Encoding.ASCII,          // ASCII
                    Encoding.Unicode,        // UTF-16 LE
                    Encoding.BigEndianUnicode // UTF-16 BE
                };

                foreach (Encoding encoding in encodingsToTry)
                {
                    try
                    {
                        // 读取文件内容
                        string[] lines = File.ReadAllLines(filePath, encoding);

                        // 检查是否包含乱码字符（简单检测）
                        bool hasGarbledText = false;
                        foreach (string line in lines)
                        {
                            if (!string.IsNullOrEmpty(line))
                            {
                                // 检查是否包含替换字符（�）或其他明显的乱码标识
                                if (line.Contains('\uFFFD') || line.Contains('?'))
                                {
                                    // 进一步检查：如果大部分字符都是问号或特殊字符，可能是乱码
                                    int specialCharCount = 0;
                                    foreach (char c in line)
                                    {
                                        if (c == '?' || c == '\uFFFD' || c == '�')
                                            specialCharCount++;
                                    }

                                    // 如果特殊字符超过50%，认为是乱码
                                    if (specialCharCount > line.Length * 0.5)
                                    {
                                        hasGarbledText = true;
                                        break;
                                    }
                                }
                            }
                        }

                        // 如果没有检测到乱码，返回结果
                        if (!hasGarbledText)
                        {
                            ETLogManager.Info(typeof(ETFile), $"成功使用 {encoding.EncodingName} 编码读取文件: {filePath}");
                            return lines;
                        }
                    }
                    catch (Exception)
                    {
                        // 当前编码失败，尝试下一个
                        continue;
                    }
                }

                // 如果所有编码都失败，使用UTF-8作为最后的尝试
                ETLogManager.Warning(typeof(ETFile), $"无法确定文件编码，使用UTF-8读取: {filePath}");
                return File.ReadAllLines(filePath, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETFile), $"读取文件失败: {filePath}", ex);
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// 将文本追加到文件，支持超长文件名
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <param name="contents">要追加的内容</param>
        /// <remarks>此方法通过添加长路径前缀来处理超长路径问题</remarks>
        public static void FileAppendText(this string path, string contents)
        {
            string longPath = PathAddLongPathPrefix(path);
            File.AppendAllText(longPath, contents);
        }

        #endregion LongPath

        #region 文件打开操作

        /// <summary>
        /// 通用文件打开方法，根据文件类型和路径类型智能选择打开方式
        /// </summary>
        /// <param name="path">文件或文件夹路径</param>
        /// <param name="excelApplication">Excel应用程序实例（可选，用于Excel文件的高级处理）</param>
        /// <returns>是否成功打开</returns>
        /// <remarks>
        /// 此方法会：
        /// 1. 检查路径是否为文件夹，如果是则用资源管理器打开
        /// 2. 检查文件是否存在
        /// 3. 对于Excel文件(.xls, .xlsx)，智能选择合适的Office应用程序：
        ///    - 如果提供了Excel应用程序实例，则使用该实例打开
        ///    - 如果当前运行在WPS环境中，则使用WPS打开
        ///    - 否则使用系统默认程序打开
        /// 4. 对于其他文件类型，使用系统默认程序打开
        /// </remarks>
        public static bool OpenFileOrDirectory(string path, object excelApplication = null)
        {
            try
            {
                if (string.IsNullOrEmpty(path))
                    return false;

                // 检查是否为文件夹
                if (Directory.Exists(path))
                {
                    System.Diagnostics.Process.Start("explorer.exe", path);
                    return true;
                }

                // 检查文件是否存在
                if (!File.Exists(path))
                {
                    System.Windows.Forms.MessageBox.Show($"文件不存在：{path}", "错误",
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    return false;
                }

                // 获取文件扩展名
                string extension = Path.GetExtension(path).ToLower();

                // 对于Excel文件，进行智能处理
                if (extension == ".xls" || extension == ".xlsx")
                {
                    return OpenExcelFileIntelligently(path, excelApplication);
                }

                // 对于其他文件类型，使用系统默认程序打开
                System.Diagnostics.Process.Start(path);
                return true;
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"打开文件失败：{ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 智能打开Excel文件的方法，根据当前环境选择合适的Office应用程序
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <param name="excelApplication">Excel应用程序实例（可选）</param>
        /// <returns>是否成功打开</returns>
        /// <remarks>
        /// 此方法会根据以下优先级选择打开方式：
        /// 1. 如果提供了Excel应用程序实例，则使用该实例打开
        /// 2. 如果当前运行在WPS环境中，则使用WPS打开
        /// 3. 否则使用系统默认程序打开
        /// 这样可以避免在WPS环境中错误地使用Excel打开文件的问题
        /// </remarks>
        public static bool OpenExcelFileIntelligently(string filePath, object excelApplication = null)
        {
            try
            {
                // 优先级1：如果提供了Excel应用程序实例，使用该实例打开
                if (excelApplication != null)
                {
                    return OpenExcelFile(filePath, excelApplication);
                }

                // 优先级2：检测当前是否为WPS环境
                try
                {
                    // 尝试调用ETExcelExtensions.IsWPS()方法
                    // 使用反射来避免直接依赖，以防该类不可用
                    var etExcelExtensionsType = Type.GetType("ET.ETExcelExtensions, ExtensionsTools");
                    if (etExcelExtensionsType != null)
                    {
                        var isWpsMethod = etExcelExtensionsType.GetMethod("IsWPS", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                        if (isWpsMethod != null)
                        {
                            bool isWps = (bool)isWpsMethod.Invoke(null, null);
                            if (isWps)
                            {
                                // 当前在WPS环境中，使用WPS打开文件
                                var openFileByWpsMethod = etExcelExtensionsType.GetMethod("OpenFileByWps", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                                if (openFileByWpsMethod != null)
                                {
                                    openFileByWpsMethod.Invoke(null, new object[] { filePath });
                                    return true;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 如果WPS检测失败，记录但不影响后续流程
                    System.Diagnostics.Debug.WriteLine($"WPS检测失败：{ex.Message}");
                }

                // 优先级3：使用系统默认程序打开
                System.Diagnostics.Process.Start(filePath);
                return true;
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"智能打开Excel文件失败：{ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 打开Excel文件的专用方法
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <param name="excelApplication">Excel应用程序实例</param>
        /// <returns>是否成功打开</returns>
        /// <remarks>
        /// 此方法会：
        /// 1. 检查文件是否已经在Excel中打开
        /// 2. 如果已打开，则激活对应的工作簿
        /// 3. 如果未打开，则打开新的工作簿
        /// </remarks>
        public static bool OpenExcelFile(string filePath, object excelApplication)
        {
            try
            {
                // 使用反射来处理Excel应用程序，避免直接依赖Excel Interop
                var applicationType = excelApplication.GetType();
                var workbooksProperty = applicationType.GetProperty("Workbooks");
                var workbooks = workbooksProperty?.GetValue(excelApplication);

                if (workbooks != null)
                {
                    // 检查文件是否已经打开
                    var workbooksType = workbooks.GetType();
                    var enumerator = workbooks as System.Collections.IEnumerable;

                    if (enumerator != null)
                    {
                        foreach (var workbook in enumerator)
                        {
                            var workbookType = workbook.GetType();
                            var fullNameProperty = workbookType.GetProperty("FullName");
                            var fullName = fullNameProperty?.GetValue(workbook) as string;

                            if (string.Equals(fullName, filePath, StringComparison.OrdinalIgnoreCase))
                            {
                                // 文件已打开，激活该工作簿
                                var activateMethod = workbookType.GetMethod("Activate");
                                activateMethod?.Invoke(workbook, null);
                                return true;
                            }
                        }
                    }

                    // 文件未打开，打开新的工作簿
                    var openMethod = workbooksType.GetMethod("Open", new[] { typeof(string) });
                    openMethod?.Invoke(workbooks, new object[] { filePath });
                    return true;
                }

                // 如果反射失败，回退到系统默认打开方式
                System.Diagnostics.Process.Start(filePath);
                return true;
            }
            catch (Exception ex)
            {
                // 如果Excel特殊处理失败，回退到系统默认打开方式
                try
                {
                    System.Diagnostics.Process.Start(filePath);
                    return true;
                }
                catch
                {
                    System.Windows.Forms.MessageBox.Show($"打开Excel文件失败：{ex.Message}", "错误",
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    return false;
                }
            }
        }

        /// <summary>
        /// 简化的文件打开方法，使用系统默认程序
        /// </summary>
        /// <param name="path">文件或文件夹路径</param>
        /// <returns>是否成功打开</returns>
        public static bool OpenWithDefaultProgram(string path)
        {
            return OpenFileOrDirectory(path, null);
        }

        #endregion 文件打开操作

        #region 剪贴板Excel文件检测

        /// <summary>
        /// 剪贴板来源类型
        /// </summary>
        public enum ClipboardSourceType
        {
            /// <summary>
            /// 无有效内容
            /// </summary>
            None,
            /// <summary>
            /// 文本路径
            /// </summary>
            TextPath,
            /// <summary>
            /// 复制的文件
            /// </summary>
            CopiedFile
        }

        /// <summary>
        /// 剪贴板Excel文件检测结果
        /// </summary>
        public class ClipboardExcelFileResult
        {
            /// <summary>
            /// Excel文件路径
            /// </summary>
            public string FilePath { get; set; }

            /// <summary>
            /// 剪贴板来源类型
            /// </summary>
            public ClipboardSourceType SourceType { get; set; }

            /// <summary>
            /// 是否找到有效的Excel文件
            /// </summary>
            public bool IsValid => !string.IsNullOrEmpty(FilePath);

            /// <summary>
            /// 生成用户友好的提示信息
            /// </summary>
            /// <returns>提示信息</returns>
            public string GetUserMessage()
            {
                if (!IsValid) return string.Empty;

                string fileName = Path.GetFileName(FilePath);

                return SourceType switch
                {
                    ClipboardSourceType.TextPath => $"检测到剪贴板中包含Excel文件路径：\n\n{FilePath}\n\n是否打开此文件？",
                    ClipboardSourceType.CopiedFile => $"检测到剪贴板中有复制的Excel文件：\n\n{fileName}\n路径：{FilePath}\n\n是否打开此文件？",
                    _ => $"检测到剪贴板中包含Excel文件：\n\n{FilePath}\n\n是否打开此文件？"
                };
            }
        }

        /// <summary>
        /// 获取剪贴板中的Excel文件路径（如果有效）
        /// </summary>
        /// <returns>剪贴板Excel文件检测结果</returns>
        /// <remarks>
        /// 检测顺序：
        /// 1. 首先检查剪贴板是否包含文本路径
        /// 2. 如果不是文本路径，则检查剪贴板是否有复制的文件
        /// 3. 返回检测结果，包含文件路径和来源类型
        /// </remarks>
        public static ClipboardExcelFileResult GetClipboardExcelFilePath()
        {
            try
            {
                // 第一步：检查剪贴板是否包含文本路径
                string textPath = GetClipboardTextPath();
                if (!string.IsNullOrEmpty(textPath))
                {
                    ETLogManager.Info($"检测到剪贴板中的有效Excel文件路径（文本）：{textPath}");
                    return new ClipboardExcelFileResult
                    {
                        FilePath = textPath,
                        SourceType = ClipboardSourceType.TextPath
                    };
                }

                // 第二步：检查剪贴板是否包含复制的文件
                string filePath = GetClipboardCopiedFilePath();
                if (!string.IsNullOrEmpty(filePath))
                {
                    ETLogManager.Info($"检测到剪贴板中的有效Excel文件路径（复制文件）：{filePath}");
                    return new ClipboardExcelFileResult
                    {
                        FilePath = filePath,
                        SourceType = ClipboardSourceType.CopiedFile
                    };
                }

                return new ClipboardExcelFileResult
                {
                    SourceType = ClipboardSourceType.None
                };
            }
            catch (Exception ex)
            {
                ETLogManager.Debug("检测剪贴板Excel文件路径时出错", ex);
                return new ClipboardExcelFileResult
                {
                    SourceType = ClipboardSourceType.None
                };
            }
        }

        /// <summary>
        /// 获取剪贴板中的文本路径
        /// </summary>
        /// <returns>有效的Excel文件路径，如果无效则返回null</returns>
        private static string GetClipboardTextPath()
        {
            try
            {
                // 检查剪贴板是否包含文本
                if (!Clipboard.ContainsText())
                {
                    return null;
                }

                string clipboardText = Clipboard.GetText().Trim();

                // 检查是否为空或过长（避免处理非路径的大量文本）
                if (string.IsNullOrEmpty(clipboardText) || clipboardText.Length > 500)
                {
                    return null;
                }

                // 检查是否为有效的文件路径格式
                if (!IsValidFilePath(clipboardText))
                {
                    return null;
                }

                // 检查文件是否存在
                if (!File.Exists(clipboardText))
                {
                    return null;
                }

                // 检查是否为Excel文件
                if (!IsExcelFile(clipboardText))
                {
                    return null;
                }

                return clipboardText;
            }
            catch (Exception ex)
            {
                ETLogManager.Debug("检测剪贴板文本路径时出错", ex);
                return null;
            }
        }

        /// <summary>
        /// 获取剪贴板中复制的文件路径
        /// </summary>
        /// <returns>有效的Excel文件路径，如果无效则返回null</returns>
        private static string GetClipboardCopiedFilePath()
        {
            try
            {
                // 检查剪贴板是否包含文件列表
                if (!Clipboard.ContainsFileDropList())
                {
                    return null;
                }

                // 获取文件列表
                var fileList = Clipboard.GetFileDropList();
                if (fileList == null || fileList.Count == 0)
                {
                    return null;
                }

                // 遍历文件列表，查找第一个Excel文件
                foreach (string filePath in fileList)
                {
                    if (string.IsNullOrEmpty(filePath))
                        continue;

                    // 检查文件是否存在
                    if (!File.Exists(filePath))
                        continue;

                    // 检查是否为Excel文件
                    if (!IsExcelFile(filePath))
                        continue;

                    // 找到第一个有效的Excel文件就返回
                    ETLogManager.Info($"在剪贴板复制文件中找到Excel文件：{filePath}");
                    return filePath;
                }

                return null;
            }
            catch (Exception ex)
            {
                ETLogManager.Debug("检测剪贴板复制文件时出错", ex);
                return null;
            }
        }

        /// <summary>
        /// 检查字符串是否为有效的文件路径格式
        /// </summary>
        /// <param name="path">要检查的路径字符串</param>
        /// <returns>是否为有效的文件路径格式</returns>
        private static bool IsValidFilePath(string path)
        {
            try
            {
                // 检查是否包含非法字符
                if (path.IndexOfAny(Path.GetInvalidPathChars()) >= 0)
                {
                    return false;
                }

                // 检查是否为绝对路径（包含盘符或UNC路径）
                if (!Path.IsPathRooted(path))
                {
                    return false;
                }

                // 尝试获取文件名，如果抛出异常则说明路径格式无效
                string fileName = Path.GetFileName(path);
                if (string.IsNullOrEmpty(fileName))
                {
                    return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否为Excel文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为Excel文件</returns>
        private static bool IsExcelFile(string filePath)
        {
            try
            {
                string extension = Path.GetExtension(filePath).ToLowerInvariant();

                // 支持的Excel文件扩展名
                string[] excelExtensions = { ".xls", ".xlsx", ".xlsm", ".xlsb", ".xltx", ".xltm", ".xlt" };

                return excelExtensions.Contains(extension);
            }
            catch
            {
                return false;
            }
        }

        #endregion 剪贴板Excel文件检测
    }
}