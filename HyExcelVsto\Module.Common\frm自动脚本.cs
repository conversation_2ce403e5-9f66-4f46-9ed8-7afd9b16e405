﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;
using Range = Microsoft.Office.Interop.Excel.Range;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 自动脚本窗体类
    /// </summary>
    public partial class frm自动脚本 : Form
    {
        #region 变量

        public Application XlApp;
        public Worksheet autoExecuteControlWorksheet;
        public Worksheet autoUpdateControlSheet;
        public HHExcelUpdater excelUpdater = new();

        private bool _worksheetVisible;
        private readonly Dictionary<int, int> _checkedListBox脚本RowIndex = [];

        // 列号常量定义
        private const int 可用命令名_列号 = 26;

        private const int 可用候选项_列号 = 27;
        private const int 可用命令行状态_列号 = 28;
        private const int 动作集合名_列号 = 1;
        private const int 命令行状态_列号 = 2;
        private const int 命令行_列号 = 3;
        private const int 作用对象_列号 = 4;
        private const int 参数1_列号 = 5;
        private const int 参数2_列号 = 6;
        private const int 参数3_列号 = 7;
        private const int 参数4_列号 = 8;
        private const int 输出1_列号 = 9;
        private const int 输出2_列号 = 10;
        private const int 备注_列号 = 11;

        // 按钮文本常量定义
        private const string 隐藏脚本表text = "隐藏脚本表";

        private const string 打开脚本表text = "打开脚本表";
        private const string 隐藏更新数据策略表text = "隐藏更新数据策略表";
        private const string 打开更新数据策略表text = "打开更新数据策略表";

        #endregion 变量

        #region 初始化

        /// <summary>
        /// 初始化自动脚本窗体
        /// </summary>
        /// <param name="addTabel">是否添加表格，默认为true</param>
        /// <returns>初始化是否成功</returns>
        public bool 初始化(bool addTabel = true)
        {
            // 获取自动执行控制工作表
            autoExecuteControlWorksheet = XlApp.ActiveWorkbook.GetWorksheetByName("autoExecute");
            // 获取自动更新控制工作表
            autoUpdateControlSheet = XlApp.ActiveWorkbook.GetWorksheetByName("autoUpdate");

            // 处理自动执行控制工作表
            if (autoExecuteControlWorksheet == null)
            {
                if (!addTabel)
                {
                    弹出修改控制表提示();
                    ETExcelExtensions.SetAppNormalMode(true);
                    return false;
                }
                弹出修改控制表提示();
                autoExecuteControlWorksheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoExecute",
                    XlApp.ActiveWorkbook);
            }

            // 处理自动更新控制工作表
            if (autoUpdateControlSheet == null && addTabel)
            {
                autoUpdateControlSheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoUpdate",
                    XlApp.ActiveWorkbook);
            }

            // 检查自动执行控制工作表是否存在
            if (autoExecuteControlWorksheet == null)
            {
                textboxLog.WriteLog("找不到控制列表");
                ETExcelExtensions.SetAppNormalMode(true);
                return false;
            }

            // 填入候选信息
            填入候选信息();

            try
            {
                // 设置单元格值和下拉列表
                autoExecuteControlWorksheet.Cells[1, 命令行状态_列号].Value = "动作状态";
                设置下拉列表(命令行_列号, 可用命令名_列号);
                设置下拉列表(命令行状态_列号, 可用命令行状态_列号);
                设置下拉列表(参数1_列号, 可用候选项_列号);
                设置下拉列表(参数2_列号, 可用候选项_列号);
                设置下拉列表(参数3_列号, 可用候选项_列号);
                设置下拉列表(参数4_列号, 可用候选项_列号);
            }
            catch (Exception ex)
            {
                textboxLog.WriteLog(ex.Message);
            }

            ETExcelExtensions.SetAppNormalMode(true);
            return true;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="worksheetVisible">工作表是否可见</param>
        /// <param name="timerInterval">定时器间隔</param>
        public frm自动脚本(bool worksheetVisible = true, int timerInterval = 2000)
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
            _worksheetVisible = worksheetVisible;

            timerCloseWindow.Interval = timerInterval;

            if (!worksheetVisible)
            {
                调整控件位置和可见性();
            }
        }

        /// <summary>
        /// 调整控件位置和可见性
        /// </summary>
        private void 调整控件位置和可见性()
        {
            textboxLog.Left = 6;
            textboxLog.Top = 3;
            Height = 206;

            button执行脚本.Visible = false;
            button打开脚本表.Visible = false;
            checkedListBox脚本.Visible = false;
            checkedListBox分类.Visible = false;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        private void frm自动脚本_Load(object sender, EventArgs e)
        {
            重新加载ToolStripMenuItem_Click(sender, e);

            if (autoUpdateControlSheet != null && _worksheetVisible)
            {
                button打开脚本表.Text = 隐藏更新数据策略表text;
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVisible;
            }

            if (autoExecuteControlWorksheet != null && _worksheetVisible)
            {
                button打开脚本表.Text = 隐藏脚本表text;
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVisible;
                autoExecuteControlWorksheet.Activate();
            }
        }

        /// <summary>
        /// 窗体关闭事件处理
        /// </summary>
        private void frm自动脚本_FormClosed(object sender, FormClosedEventArgs e)
        {
            初始化(false);
            if (autoExecuteControlWorksheet != null && _worksheetVisible)
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVeryHidden;

            if (autoUpdateControlSheet != null && _worksheetVisible)
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVeryHidden;
        }

        /// <summary>
        /// 设置指定列的下拉列表
        /// </summary>
        /// <param name="targetColumn">目标列号</param>
        /// <param name="sourceColumn">源列号</param>
        private void 设置下拉列表(int targetColumn, int sourceColumn)
        {
            ETExcelExtensions.SetOptional设置下拉可选项(
                autoExecuteControlWorksheet.Columns[targetColumn],
                "=" + autoExecuteControlWorksheet.Columns[sourceColumn].Address);
        }

        /// <summary>
        /// 填入候选信息
        /// </summary>
        private void 填入候选信息()
        {
            if (autoExecuteControlWorksheet == null)
                return;

            try
            {
                ETExcelExtensions.SetAppFastMode();
                填入列数据(可用命令名_列号, 可用命令名Array);
                填入列数据(可用候选项_列号, 可用候选项Array);
                填入列数据(可用命令行状态_列号, 命令行状态Array);
                ETExcelExtensions.SetAppNormalMode(true);
            }
            catch (Exception)
            {
                // 忽略异常
            }
        }

        /// <summary>
        /// 弹出修改控制表提示
        /// </summary>
        private void 弹出修改控制表提示()
        {
            // 兼容老控制表，提示修正控制表
            Worksheet tmpWs = XlApp.ActiveWorkbook.GetWorksheetByName("RunByList");
            if (tmpWs == null)
                return;

            textboxLog.WriteLog("启用新版控制表");
            MessageBox.Show(
                "启用新版控制表 autoExecute，原控制表RunByList失效",
                "提示",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// 填入列数据
        /// </summary>
        /// <param name="columnIndex">列索引</param>
        /// <param name="dataArray">数据数组</param>
        private void 填入列数据(int columnIndex, string[] dataArray)
        {
            for (int i = 1; i <= dataArray.Length; i++)
                autoExecuteControlWorksheet.Columns[columnIndex].Cells[i, 1].Value = dataArray[i - 1];
        }

        #endregion 初始化

        #region 按键响应

        /// <summary>
        /// 打开/隐藏脚本表按钮点击事件
        /// </summary>
        private void button打开脚本表_Click(object sender, EventArgs e)
        {
            if (autoExecuteControlWorksheet == null)
            {
                弹出修改控制表提示();
                autoExecuteControlWorksheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoExecute",
                    XlApp.ActiveWorkbook);
            }

            if (autoExecuteControlWorksheet == null)
            {
                button打开脚本表.Text = 打开脚本表text;
                MessageBox.Show("模版文件缺少对应子表");
                return;
            }

            切换脚本表可见性();
        }

        /// <summary>
        /// 切换脚本表可见性
        /// </summary>
        private void 切换脚本表可见性()
        {
            if (button打开脚本表.Text == 打开脚本表text)
            {
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVisible;
                autoExecuteControlWorksheet.Activate();
                button打开脚本表.Text = 隐藏脚本表text;
                重新加载ToolStripMenuItem_Click(null, EventArgs.Empty);
            }
            else
            {
                autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVeryHidden;
                button打开脚本表.Text = 打开脚本表text;
            }
        }

        /// <summary>
        /// 打开/隐藏更新数据策略表按钮点击事件
        /// </summary>
        private void button打开更新数据策略表_Click(object sender, EventArgs e)
        {
            if (autoUpdateControlSheet == null)
            {
                弹出修改控制表提示();
                autoUpdateControlSheet = ETExcelExtensions.CopyWorksheetFromTemplateToWorkbook(
                    "autoUpdate",
                    XlApp.ActiveWorkbook);
            }

            if (autoUpdateControlSheet == null)
            {
                button打开更新数据策略表.Text = 打开更新数据策略表text;
                MessageBox.Show("模版文件缺少对应子表");
                return;
            }

            切换更新数据策略表可见性();
        }

        /// <summary>
        /// 切换更新数据策略表可见性
        /// </summary>
        private void 切换更新数据策略表可见性()
        {
            if (button打开更新数据策略表.Text == 打开更新数据策略表text)
            {
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVisible;
                autoUpdateControlSheet.Activate();
                button打开更新数据策略表.Text = 隐藏更新数据策略表text;
            }
            else
            {
                autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVeryHidden;
                button打开更新数据策略表.Text = 打开更新数据策略表text;
            }
        }

        /// <summary>
        /// 重新加载菜单项点击事件
        /// </summary>
        private void 重新加载ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            清空列表框();

            // 如果初始化失败，则直接返回
            if (!初始化(false))
                return;

            加载分类列表();
        }

        /// <summary>
        /// 清空列表框
        /// </summary>
        private void 清空列表框()
        {
            checkedListBox分类.Items.Clear();
            checkedListBox脚本.Items.Clear();
        }

        /// <summary>
        /// 加载分类列表
        /// </summary>
        private void 加载分类列表()
        {
            // 从Excel范围获取值列表并转换为字符串列表
            List<string> valueList = ETExcelExtensions.ConvertRangeToStringList(
                autoExecuteControlWorksheet.Range["A2:A10000"],
                true);
            if (valueList == null)
                return;

            // 将字符串列表填充到"分类"CheckedListBox控件中
            ETForm.LoadCheckedListBox(checkedListBox分类, valueList);

            // 遍历分类项目，将带有'---'前缀的项设置为不确定状态，并取消选中
            for (int i = 0; i < checkedListBox分类.Items.Count; i++)
                if (checkedListBox分类.Items[i].ToString().StartsWith("---"))
                {
                    checkedListBox分类.SetItemCheckState(i, CheckState.Indeterminate);
                    checkedListBox分类.SetItemChecked(i, false);
                }
        }

        /// <summary>
        /// 全选菜单项点击事件
        /// </summary>
        private void 全选ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 选中脚本清单中的所有项，并在结果文本框中记录操作
            for (int i = 0; i < checkedListBox脚本.Items.Count; i++)
                checkedListBox脚本.SetItemChecked(i, true);
            textboxLog.WriteLog("全选");
        }

        /// <summary>
        /// 全取消菜单项点击事件
        /// </summary>
        private void 全取消ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 取消选中脚本清单中的所有项，并在结果文本框中记录操作
            for (int i = 0; i < checkedListBox脚本.Items.Count; i++)
                checkedListBox脚本.SetItemChecked(i, false);
            textboxLog.WriteLog("全取消");
        }

        /// <summary>
        /// 分类项检查事件
        /// </summary>
        private void 分类项检查事件_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            // 当当前状态为不确定时，保持不变
            if (e.CurrentValue == CheckState.Indeterminate)
                e.NewValue = CheckState.Indeterminate;
        }

        /// <summary>
        /// 分类列表选择变更事件
        /// </summary>
        private void CheckedListBox分类_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 清空脚本清单和相关索引数据
            checkedListBox脚本.Items.Clear();
            _checkedListBox脚本RowIndex.Clear();

            // 获取已选择分类项的集合
            HashSet<string> selectedCategories = GetSelectedCategories();

            // 遍历Excel范围内的每一行，根据选择的分类项添加对应的脚本到脚本清单中
            Range rng = ETExcelExtensions.OptimizeRangeSize(autoExecuteControlWorksheet.Range["A2:B1000"]);
            foreach (Range rngRow in rng.Rows)
            {
                if (IsValidRow(rngRow) && selectedCategories.Contains(rngRow.Cells[1, 动作集合名_列号].Value.ToString()))
                {
                    AddScriptToList(rngRow);
                }
            }
        }

        /// <summary>
        /// 获取已选择的分类
        /// </summary>
        /// <returns>已选择的分类集合</returns>
        private HashSet<string> GetSelectedCategories()
        {
            HashSet<string> selectedCategories = [];
            for (int i = 0; i < checkedListBox分类.Items.Count; i++)
                if (checkedListBox分类.GetItemChecked(i) && !checkedListBox分类.Items[i].ToString().StartsWith("---"))
                    selectedCategories.Add(checkedListBox分类.Items[i].ToString());
            return selectedCategories;
        }

        /// <summary>
        /// 检查行是否有效
        /// </summary>
        /// <param name="rngRow">行范围</param>
        /// <returns>是否有效</returns>
        private bool IsValidRow(Range rngRow)
        {
            return !ETExcelExtensions.IsCellEmpty(rngRow.Cells[1, 动作集合名_列号]) &&
                !ETExcelExtensions.IsCellEmpty(rngRow.Cells[1, 命令行_列号]);
        }

        /// <summary>
        /// 添加脚本到列表
        /// </summary>
        /// <param name="rngRow">行范围</param>
        private void AddScriptToList(Range rngRow)
        {
            checkedListBox脚本.Items.Add(rngRow.Cells[1, 命令行_列号].Value.ToString());
            checkedListBox脚本.SetItemChecked(checkedListBox脚本.Items.Count - 1, true);
            _checkedListBox脚本RowIndex.Add(rngRow.Row, checkedListBox脚本.Items.Count - 1);
        }

        /// <summary>
        /// 执行脚本按钮点击事件
        /// </summary>
        private void Button执行脚本_Click(object sender, EventArgs e)
        { Run执行脚本(); }

        /// <summary>
        /// 清空日志菜单项点击事件
        /// </summary>
        private void 清空ToolStripMenuItem_Click(object sender, EventArgs e)
        { textboxLog.Text = string.Empty; }

        /// <summary>
        /// 结果文本框鼠标悬停事件
        /// </summary>
        private void TextBox结果_MouseHover(object sender, EventArgs e)
        { timerCloseWindow.Enabled = false; }

        /// <summary>
        /// 检查范围是否为空并输出消息
        /// </summary>
        /// <param name="rng">范围</param>
        /// <param name="msg">消息</param>
        /// <returns>范围是否为空</returns>
        private bool CheckRangeIsNullAndOutputMsg(Range rng, string msg)
        {
            if (rng == null)
            {
                textboxLog.WriteLog($"{DateTime.Now:mm:ss}  {msg}  目标对象找不到");
                return true;
            }

            textboxLog.WriteLog($"{DateTime.Now:mm:ss}  {msg}({rng.Parent.Name}!{rng.Address})");
            return false;
        }

        /// <summary>
        /// 打印消息
        /// </summary>
        /// <param name="method">方法名</param>
        /// <param name="msg">附加消息</param>
        private void PrintMessage(string method, string msg = null)
        { textboxLog.WriteLog($"   执行完成{(msg == null ? string.Empty : $",{msg}")}", false); }

        /// <summary>
        /// 关闭窗口定时器事件
        /// </summary>
        private void TimerCloseWindow_Tick(object sender, EventArgs e)
        { Close(); }

        /// <summary>
        /// 通知消息
        /// </summary>
        /// <param name="msg">消息内容</param>
        private void NotifyMessage(string msg)
        {
            // 此方法目前为空，可以根据需要实现通知逻辑
        }

        #endregion 按键响应
    }
}